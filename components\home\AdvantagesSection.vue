<template>
  <section class="advantages-section">
    <div class="section-container">
      <h2 class="section-title">公司优势</h2>
      <div class="advantages-grid">
        <div v-for="advantage in advantages" 
             :key="advantage.id"
             class="advantage-item"
             data-aos="fade-up">
          <div class="advantage-number">{{ advantage.number }}</div>
          <div class="advantage-label">{{ advantage.label }}</div>
          <div class="advantage-desc">{{ advantage.description }}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const advantages = [
  {
    id: 1,
    number: '10年+',
    label: '行业经验',
    description: '深耕建筑信息化领域'
  },
  {
    id: 2,
    number: '100+',
    label: '服务项目',
    description: '覆盖全国多个省市'
  },
  {
    id: 3,
    number: '50+',
    label: '技术专利',
    description: '持续创新研发'
  },
  {
    id: 4,
    number: '200+',
    label: '专业团队',
    description: '技术过硬实力雄厚'
  }
]
</script>

<style scoped>
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.advantage-item {
  text-align: center;
  padding: 40px 30px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  transition: transform 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-10px);
}

.advantage-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: #003366;
  margin-bottom: 15px;
}

.advantage-label {
  font-size: 1.3rem;
  color: #001529;
  margin-bottom: 10px;
}

.advantage-desc {
  color: #666;
}
</style>