<template>
  <section class="services-section">
    <div class="section-container">
      <h2 class="section-title">核心业务</h2>
      <div class="services-grid">
        <div v-for="(service, index) in services" 
             :key="index"
             class="service-card"
             :data-aos="service.animation">
          <div class="service-icon">
            <el-icon :size="60"><component :is="service.icon" /></el-icon>
          </div>
          <h3>{{ service.title }}</h3>
          <p>{{ service.description }}</p>
          <ul class="service-features">
            <li v-for="(feature, idx) in service.features" 
                :key="idx">{{ feature }}</li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { Monitor, Tools, DataLine } from '@element-plus/icons-vue'

const services = [
  {
    icon: Monitor,
    title: '智慧建造平台',
    description: '打造数字化工程管理体系',
    features: ['BIM技术应用', '智能建造方案', '全过程数字化管理'],
    animation: 'fade-right'
  },
  {
    icon: Tools,
    title: '技术咨询服务',
    description: '提供专业技术支持',
    features: ['技术培训指导', '方案设计优化', '项目实施管理'],
    animation: 'fade-up'
  },
  {
    icon: DataLine,
    title: '数字孪生技术',
    description: '实现虚实结合的智能管理',
    features: ['实时数据监控', '智能分析决策', '可视化管理'],
    animation: 'fade-left'
  }
]
</script>

<style scoped>
.services-section {
  padding: 80px 0;
  background: #f8f9fa;
  position: relative;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #001529;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #003366;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 40px;
  padding: 20px 0;
}

.service-card {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.service-icon {
  color: #003366;
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
}

.service-card h3 {
  font-size: 1.5rem;
  color: #001529;
  margin-bottom: 15px;
  text-align: center;
}

.service-card p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
  text-align: center;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 20px 0;
  flex-grow: 1;
}

.service-features li {
  margin: 12px 0;
  padding-left: 25px;
  position: relative;
  color: #333;
}

.service-features li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: #003366;
}

@media (max-width: 768px) {
  .services-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .service-card {
    padding: 30px;
  }
}
</style>