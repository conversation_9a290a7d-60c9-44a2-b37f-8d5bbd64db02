<template>
  <section class="cases-section">
    <div class="section-container">
      <h2 class="section-title">成功案例</h2>
      <el-carousel :interval="4000" type="card" height="400px">
        <el-carousel-item v-for="item in caseItems" :key="item.id">
          <div class="case-item">
            <el-image :src="item.image" fit="cover" />
            <div class="case-info">
              <h3>{{ item.title }}</h3>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </section>
</template>

<script setup lang="ts">
const caseItems = [
  {
    id: 1,
    image: '/images/case1.jpg',
    title: '郑州地铁BIM应用',
    description: '全过程数字化管理解决方案'
  },
  {
    id: 2,
    image: '/images/case2.jpg',
    title: '智慧工地管理平台',
    description: '打造数字化施工现场'
  },
  {
    id: 3,
    image: '/images/case3.jpg',
    title: '市政工程数字孪生',
    description: '实现智能化运维管理'
  }
]
</script>

<style scoped>
.cases-section {
  background: #f8f9fa;
}

.case-item {
  position: relative;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.case-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30px;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
}

.case-info h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.case-info p {
  opacity: 0.9;
}

:deep(.el-carousel__item) {
  border-radius: 15px;
}
</style>