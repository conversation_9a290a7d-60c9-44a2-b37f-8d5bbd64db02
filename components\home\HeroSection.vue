<template>
  <div class="hero-section">
    <div class="hero-content">
      <h1 class="main-title">河南路建科技</h1>
      <p class="sub-title">数字赋能建筑 · 科技引领未来</p>
      <div class="nav-buttons">
        <el-button
          v-for="nav in navButtons"
          :key="nav.id"
          class="nav-btn"
          @click="scrollToSection(nav.target)"
        >
          {{ nav.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const navButtons = [
    { id: 1, text: '核心业务', target: '.services-section' },
    { id: 2, text: '赛事专区', target: '.competitions-section' },
    { id: 3, text: '联系我们', target: '.contact-section' },
  ];

  const scrollToSection = (target: string) => {
    const element = document.querySelector(target);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };
</script>

<style scoped>
  .hero-section {
    height: 100vh;
    background: linear-gradient(135deg, #001529 0%, #003366 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
  }

  .hero-content {
    z-index: 2;
    padding: 0 20px;
  }

  .main-title {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .sub-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }

  .feature-tags {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 40px;
  }

  .tag {
    padding: 12px 30px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    backdrop-filter: blur(10px);
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .tag:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
  }

  .nav-buttons {
    margin-top: 60px;
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-btn {
    min-width: 160px;
    height: 50px;
    font-size: 1.1rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .nav-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .nav-btn:active {
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    .main-title {
      font-size: 3rem;
    }

    .sub-title {
      font-size: 1.4rem;
    }

    .nav-buttons {
      margin-top: 40px;
      gap: 20px;
    }

    .nav-btn {
      min-width: 140px;
      height: 45px;
      font-size: 1rem;
    }
  }
</style>
