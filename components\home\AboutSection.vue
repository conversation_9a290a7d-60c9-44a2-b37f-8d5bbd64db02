<template>
  <section class="about-section">
    <div class="section-container">
      <h2 class="section-title">关于我们</h2>
      <div class="about-content">
        <div class="about-text">
          <p>
            河南路建科技是一家专注于建筑信息化领域的高新技术企业，致力于为建筑行业提供全方位的数字化解决方案。
          </p>
          <p>
            作为中国交通运输协会认定的技术服务机构，我们承办"石金杯"系列大赛，推动行业技术创新与发展。
          </p>
        </div>
        <div class="about-image">
          <el-image src="/images/company.png" fit="cover" />
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
  .about-section {
    padding: 80px 0;
    background: #fff;
    position: relative;
  }

  .section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #001529;
    position: relative;
  }

  .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #003366;
  }

  .about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
  }

  .about-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
  }

  .about-text p {
    margin-bottom: 20px;
    opacity: 0.9;
  }

  .about-image {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    height: 320px;
  }

  .about-image :deep(.el-image) {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .about-image:hover :deep(.el-image) {
    transform: scale(1.05);
  }

  .learn-more-btn {
    margin-top: 20px;
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
    transition: all 0.3s ease;
  }

  .learn-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 51, 102, 0.2);
  }

  @media (max-width: 768px) {
    .about-section {
      padding: 60px 0;
    }

    .section-title {
      font-size: 2rem;
      margin-bottom: 40px;
    }

    .about-content {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .about-image {
      height: 300px;
      order: -1;
    }

    .about-text {
      text-align: center;
    }
  }
</style>
