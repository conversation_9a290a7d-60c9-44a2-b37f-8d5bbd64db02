<template>
  <section class="competitions-section">
    <div class="section-container">
      <h2 class="section-title">赛事专区</h2>
      <div class="competitions-grid">
        <div
          v-for="competition in competitions"
          :key="competition.id"
          class="competition-item"
          :data-aos="competition.animation"
          :data-aos-delay="competition.delay"
        >
          <div class="competition-banner">
            <el-image :src="competition.image" fit="cover" />
            <div class="competition-status">进行中</div>
          </div>
          <div class="competition-content">
            <h3>{{ competition.title }}</h3>
            <p>{{ competition.description }}</p>
            <div class="button-group">
              <el-button
                type="primary"
                @click="handleSignup(competition.signupUrl)"
              >
                立即报名
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { ArrowRight } from '@element-plus/icons-vue';

  const competitions = [
    {
      id: 1,
      title: '第二届石金杯BIM大赛',
      description: '聚焦BIM创新技术应用，推动数字建筑发展',
      image: '/images/bim-competition.png',
      url: 'https://sjb.bimetc.cn',
      signupUrl: 'https://sjb.bimetc.cn/',
      animation: 'fade-up',
      delay: 0,
    },
    {
      id: 2,
      title: '首届石金杯施工工法大赛',
      description: '推动施工创新技术，提升工程建设品质',
      image: '/images/construction-competition.png',
      url: 'https://gf.bimetc.cn',
      signupUrl: 'https://gf.bimetc.cn/',
      animation: 'fade-up',
      delay: 100,
    },
    {
      id: 3,
      title: '首届石金杯勘察设计大赛',
      description: '促进勘察设计创新，引领行业发展方向',
      image: '/images/design-competition.png',
      url: 'https://sj.bimetc.cn',
      signupUrl: 'https://sj.bimetc.cn/',
      animation: 'fade-up',
      delay: 200,
    },
  ];

  const handleSignup = (url: string) => {
    window.open(url, '_blank');
  };

  const handleViewDetail = (url: string) => {
    window.open(url, '_blank');
  };
</script>

<style scoped>
  .competitions-section {
    padding: 80px 0;
    background: #fff;
    position: relative;
  }

  .section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #001529;
    position: relative;
  }

  .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #003366;
  }

  .competitions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    padding: 20px 0;
  }

  .competition-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
  }

  .competition-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .competition-banner {
    position: relative;
    height: 220px;
    overflow: hidden;
  }

  .competition-banner .el-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .competition-item:hover .competition-banner .el-image {
    transform: scale(1.05);
  }

  .competition-status {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 4px 12px; /* 减小内边距 */
    background: #00b96b;
    color: white;
    border-radius: 15px; /* 调整圆角 */
    font-size: 0.8rem; /* 减小字体 */
    z-index: 1;
    letter-spacing: 1px; /* 增加字间距 */
  }

  .competition-content {
    padding: 30px;
  }

  .competition-content h3 {
    font-size: 1.4rem;
    color: #001529;
    margin-bottom: 15px;
  }

  .competition-content p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
  }

  .competition-content :deep(.el-button) {
    font-size: 1.1rem;
  }

  .competition-content :deep(.el-button:hover) {
    opacity: 0.9;
  }

  .competition-content :deep(.el-icon) {
    margin-left: 8px;
    transition: transform 0.3s ease;
  }

  .competition-content :deep(.el-button:hover .el-icon) {
    transform: translateX(5px);
  }

  .button-group {
    display: flex;
    gap: 20px;
    justify-content: flex-start;
    align-items: center;
    margin-top: 20px;
  }

  .button-group .el-button {
    font-size: 1.1rem;
  }

  @media (max-width: 768px) {
    .button-group {
      flex-direction: column;
      align-items: stretch;
      gap: 10px;
    }
  }

  @media (max-width: 768px) {
    .competitions-section {
      padding: 60px 0;
    }

    .section-title {
      font-size: 2rem;
      margin-bottom: 40px;
    }

    .competitions-grid {
      grid-template-columns: 1fr;
      gap: 30px;
    }

    .competition-banner {
      height: 200px;
    }
  }
</style>
