<template>
  <section class="contact-section">
    <div class="contact-content">
      <h2>开启您的数字建筑之旅</h2>
      <p>专业的技术团队，为您提供一站式解决方案</p>
      <el-button 
        type="primary" 
        size="large" 
        class="contact-btn"
        @click="navigateToContact"
      >
        联系我们
      </el-button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToContact = () => {
  router.push('/contact')
}
</script>

<style scoped>
.contact-section {
  background: linear-gradient(135deg, #001529 0%, #003366 100%);
  color: white;
  text-align: center;
  padding: 100px 20px;
}

.contact-content h2 {
  font-size: 2.8rem;
  margin-bottom: 20px;
}

.contact-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.contact-btn {
  padding: 15px 40px;
  font-size: 1.2rem;
  border-radius: 30px;
  background: white;
  color: #003366;
}

.contact-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255,255,255,0.2);
}

@media (max-width: 768px) {
  .contact-content h2 {
    font-size: 2rem;
  }
}
</style>