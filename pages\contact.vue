<template>
  <div class="contact-page">
    <div class="contact-header">
      <!-- <el-button class="back-home" @click="router.push('/')">
        <el-icon :size="18"><Back /></el-icon>
        返回首页
      </el-button> -->
      <h1>联系我们</h1>
      <p>欢迎随时与我们联系，我们期待为您服务</p>
    </div>
    <div class="contact-container">
      <div class="contact-info">
        <div class="info-card">
          <el-icon :size="40"><Phone /></el-icon>
          <h3>电话咨询</h3>
          <p>15737527910</p>
          <p>周一至周五 9:00-18:00</p>
        </div>

        <div class="info-card">
          <el-icon :size="40"><Message /></el-icon>
          <h3>电子邮箱</h3>
          <p><EMAIL></p>
          <p>7*24小时接收邮件</p>
        </div>

        <div class="info-card">
          <el-icon :size="40"><Location /></el-icon>
          <h3>公司地址</h3>
          <p>河南交通职业技术学院院内</p>
          <p>郑州市中牟县通惠路259号</p>
          <p>邮编：451450</p>
        </div>
      </div>

      <div class="additional-info">
        <div class="company-intro">
          <div class="intro-header">
            <h2>关于我们</h2>
            <p>
              河南路建科技是一家专注于建筑信息化领域的高新技术企业，致力于为建筑行业提供全方位的数字化解决方案。作为中国交通运输协会认定的技术服务机构，我们承办"石金杯"系列大赛，推动行业创新技术的发展。公司位于河南交通职业技术学院院内，与学院深度合作，共同培养建筑信息化人才。
            </p>
          </div>
          <div class="company-features">
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon :size="32"><Trophy /></el-icon>
              </div>
              <div class="feature-text">
                <h4>行业认可</h4>
                <p>中国交通运输协会认定技术服务机构</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon :size="32"><School /></el-icon>
              </div>
              <div class="feature-text">
                <h4>校企合作</h4>
                <p>与河南交通职业技术学院深度合作</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon :size="32"><Promotion /></el-icon>
              </div>
              <div class="feature-text">
                <h4>赛事承办</h4>
                <p>承办"石金杯"系列BIM技能大赛</p>
              </div>
            </div>
          </div>
        </div>

        <div class="service-and-follow">
          <div class="service-hours">
            <h2>服务时间</h2>
            <div class="time-list">
              <div class="time-item">
                <span>周一至周五</span>
                <span>9:00 - 18:00</span>
              </div>
              <div class="time-item">
                <span>周六、周日</span>
                <span>休息</span>
              </div>
            </div>
          </div>

          <div class="follow-section">
            <div class="follow-text">
              <el-icon :size="24"><ChatDotRound /></el-icon>
              <h3>关注我们</h3>
              <p>扫描二维码，获取更多资讯</p>
            </div>
            <img
              src="@/assets/images/wechat-qr.jpg"
              alt="微信公众号"
              class="qr-image"
            />
          </div>
        </div>
      </div>

      <!-- <div class="qr-code-section">
        <div class="qr-card">
          <img src="/images/wechat-qr.jpg" alt="微信公众号" />
          <p>关注微信公众号</p>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    Phone,
    Message,
    Location,
    Position,
    Back,
  } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
</script>

<style scoped>
  .contact-page {
    min-height: 100vh;
    background: #f8f9fa;
  }

  .contact-header {
    background: linear-gradient(135deg, #001529 0%, #003366 100%);
    color: white;
    text-align: center;
    padding: 80px 20px;
    position: relative;
  }

  .back-home {
    position: absolute;
    left: 40px;
    top: 30px;
    font-size: 1.4rem;
    font-weight: 500;
    padding: 15px 30px;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
  }

  .contact-header h1 {
    font-size: 3rem;
    margin-bottom: 20px;
  }

  .contact-header p {
    font-size: 1.2rem;
    opacity: 0.9;
  }

  .contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px;
  }

  .contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .info-card {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .info-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .info-card .el-icon {
    color: #003366;
    margin-bottom: 20px;
  }

  .info-card h3 {
    font-size: 1.4rem;
    color: #001529;
    margin-bottom: 15px;
  }

  .info-card p {
    color: #666;
    line-height: 1.6;
  }

  .additional-info {
    margin-top: 60px;
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: 40px;
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  }

  .company-intro h2,
  .service-hours h2 {
    color: #001529;
    font-size: 1.8rem;
    margin-bottom: 20px;
  }

  .company-intro p {
    color: #666;
    line-height: 1.8;
    font-size: 1.1rem;
  }

  .time-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .time-item {
    display: flex;
    justify-content: space-between;
    color: #666;
    padding: 10px 0;
    border-bottom: 1px dashed #eee;
  }

  .time-item:last-child {
    border-bottom: none;
  }

  .qr-code-section {
    margin-top: 60px;
    display: flex;
    justify-content: center;
  }

  .qr-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  }

  .qr-card img {
    width: 200px;
    height: 200px;
    margin-bottom: 15px;
  }

  .qr-card p {
    color: #666;
    font-size: 1.1rem;
  }

  .bottom-section {
    margin-top: 60px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    align-items: start;
  }

  .map-section {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  }

  .map-section h2 {
    color: #001529;
    font-size: 1.8rem;
    margin-bottom: 30px;
  }

  .map-info {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
  }

  .location-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
    font-size: 1.1rem;
  }

  .detail-item .el-icon {
    color: #003366;
    font-size: 20px;
  }

  .map-placeholder {
    width: 100%;
    height: 300px;
    border-radius: 10px;
    overflow: hidden;
  }

  .map-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: 768px) {
    .bottom-section {
      grid-template-columns: 1fr;
    }

    .map-info {
      grid-template-columns: 1fr;
    }

    .map-placeholder {
      height: 200px;
    }
  }
</style>

<style scoped>
  .company-intro {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: flex-start;
  }

  .company-intro h2 {
    margin-top: 0;
    padding-top: 0;
  }

  .company-intro p {
    margin-top: 0;
    flex-grow: 1;
  }

  .service-and-follow {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .service-hours {
    margin-top: 0;
  }

  @media (max-width: 768px) {
    .company-intro {
      height: auto;
    }

    .service-and-follow {
      margin-top: 30px;
    }
  }
</style>
