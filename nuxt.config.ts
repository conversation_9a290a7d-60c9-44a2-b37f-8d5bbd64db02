// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  app: {
    head: {
      title: '河南路建科技 - 数字赋能建筑',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        {
          name: 'description',
          content:
            '河南路建科技是一家专注于建筑信息化领域的高新技术企业，提供BIM技术应用、智能建造方案、数字孪生等全方位数字化解决方案。',
        },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'robots', content: 'index, follow' },
        {
          name: 'keywords',
          content:
            'BIM技术,智慧建造,数字孪生,建筑信息化,工程管理,智能建造,数字化转型',
        },
        { property: 'og:title', content: '河南路建科技 - 数字赋能建筑' },
        {
          property: 'og:description',
          content:
            '河南路建科技是一家专注于建筑信息化领域的高新技术企业，提供BIM技术应用、智能建造方案、数字孪生等全方位数字化解决方案。',
        },
        { property: 'og:type', content: 'website' },
        { property: 'og:locale', content: 'zh_CN' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://bimetc.cn' }, // 记得替换为你的实际域名
      ],
    },
  },
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],

  tailwindcss: {
    exposeConfig: true,
    config: {
      content: [
        './components/**/*.{vue,js,ts}',
        './layouts/**/*.vue',
        './pages/**/*.vue',
        './app.vue',
        './error.vue',
      ],
    },
  },

  modules: ['@element-plus/nuxt'],

  compatibilityDate: '2025-03-14',
});
